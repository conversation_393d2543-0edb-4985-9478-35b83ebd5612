<template>
  <div class="export-modal">
      <div class="close-icon" @click="$emit('close')">
        <img src="/icons/close-icon.svg" alt="Close" />
      </div>

      <div class="modal-header">
        <h1 class="modal-title">Export {{ getModalTitle }}</h1>
        <p class="modal-subtitle">{{ getModalSubtitle }}</p>
      </div>

      <div class="divider"></div>
        <div class="modal-content">
        <div class="file-type-header">
          <img src="/icons/file-type-icon.svg" class="file-icon" alt="File" />
          <span class="file-type-label">Export Format</span>
        </div>

        <div class="file-type-separator"></div>

        <!-- Show zip status indicator when multiple items or folders are selected -->
        <div class="zip-status" v-if="shouldZip">
          <img :src="zipIcon" class="zip-status-icon" alt="Zip" />
          <span class="zip-status-text">Files will be exported as a zip archive</span>
        </div>

        <div class="file-options">
          <!-- PDF option -->          <div
            class="file-option-container"
            :class="{ 'selected': selectedFormat === 'pdf' }"
            @click="selectFormat('pdf')"
          >
            <div class="file-icon-container">              <div class="format-icon-wrapper">
                <img src="/icons/pdf-icon.svg" alt="PDF" class="format-icon" />
                <img v-if="shouldZip" :src="zipIcon" class="zip-icon" alt="Zip" />
              </div>
              <span>PDF</span>
            </div>
          </div>

          <!-- Markdown option -->
          <div
            class="file-option-container"
            :class="{ 'selected': selectedFormat === 'md' }"
            @click="selectFormat('md')"
          >
            <div class="file-icon-container">              <div class="format-icon-wrapper">
                <img src="/icons/md-icon.svg" alt="MD" class="format-icon" />
                <img v-if="shouldZip" :src="zipIcon" class="zip-icon" alt="Zip" />
              </div>
              <span>Markdown</span>
            </div>
          </div>

          <!-- Noti format option -->
          <div
            class="file-option-container"
            :class="{ 'selected': selectedFormat === 'noti' }"
            @click="selectFormat('noti')"
          >
            <div class="file-icon-container">              <div class="format-icon-wrapper">
                <img src="/icons/noti-icon.svg" alt="Noti" class="format-icon" />
                <img v-if="shouldZip" :src="zipIcon" class="zip-icon" alt="Zip" />
              </div>
              <span>Noti Format</span>
            </div>
          </div>
        </div>

        <!-- Only show additional options for folders -->
        <div class="export-options" v-if="anyFolders">
          <div class="export-option">
            <input type="checkbox" id="includeSubfolders" v-model="includeSubfolders" />
            <label for="includeSubfolders">Include subfolders</label>
          </div>

          <div class="export-option">
            <input type="checkbox" id="includeNotes" v-model="includeNotes" />
            <label for="includeNotes">Include notes</label>
          </div>
        </div>
      </div>

      <div class="divider"></div>

      <div class="modal-footer">
        <button class="btn btn-cancel" @click="$emit('close')">
          Cancel
        </button>
        <button class="btn btn-export" @click="exportItems" :disabled="!selectedFormat || isExporting">
          <span v-if="isExporting" class="loading-spinner"></span>
          <img v-if="!isExporting" src="/icons/export-icon.svg" class="export-icon" alt="Export" />
          {{ isExporting ? 'Preparing export...' : 'Export' }}
        </button>
      </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue';
import { useElectronAPI } from '../../useElectronAPI';

// Import icons as modules for proper path resolution in production
import zipIcon from '/icons/zip-icon.svg'

interface ExportItem {
  id: number;
  type: 'folder' | 'note';
  name: string;
}

export default defineComponent({
  name: 'ExportMultipleItemsModal',
  props: {
    items: {
      type: Array as () => ExportItem[],
      required: true,
      validator: (items: ExportItem[]) => {
        return Array.isArray(items) && items.length > 0;
      }
    },
    folderName: {
      type: String,
      default: ''
    },
    noteName: {
      type: String,
      default: ''
    }
  },
  emits: ['close', 'export-start', 'export-complete'],
  setup(props, { emit }) {
    const db = useElectronAPI();
    const selectedFormat = ref('');
    const includeSubfolders = ref(true);
    const includeNotes = ref(true);
    const isExporting = ref(false);

    // Determine if we're dealing with folders
    const anyFolders = computed(() => {
      return props.items.some(item => item.type === 'folder');
    });

    // Determine if we should zip the output
    const shouldZip = computed(() => {
      return props.items.length > 1 || anyFolders.value;
    });

    // Determine the modal title based on content
    const getModalTitle = computed(() => {
      if (props.items.length === 0) return 'Items';

      if (props.items.length === 1) {
        // Single item export
        const item = props.items[0];
        return item.type === 'folder' ? 'Folder' : 'Note';
      } else {
        // Multiple items export
        const folderCount = props.items.filter(item => item.type === 'folder').length;
        const noteCount = props.items.filter(item => item.type === 'note').length;

        if (folderCount > 0 && noteCount > 0) {
          return 'Items';
        } else if (folderCount > 0) {
          return 'Folders';
        } else {
          return 'Notes';
        }
      }
    });

    // Determine the subtitle based on content
    const getModalSubtitle = computed(() => {
      if (props.items.length === 0) return 'No items selected';

      if (props.items.length === 1) {
        // Single item export
        const item = props.items[0];
        if (item.type === 'folder') {
          return `Export "${item.name}" and its contents`;
        } else {
          return `Export "${item.name}"`;
        }
      } else {
        // Multiple items export
        const folderCount = props.items.filter(item => item.type === 'folder').length;
        const noteCount = props.items.filter(item => item.type === 'note').length;

        if (folderCount > 0 && noteCount > 0) {
          return `Export ${folderCount} folder${folderCount > 1 ? 's' : ''} and ${noteCount} note${noteCount > 1 ? 's' : ''}`;
        } else if (folderCount > 0) {
          return `Export ${folderCount} folder${folderCount > 1 ? 's' : ''}`;
        } else {
          return `Export ${noteCount} note${noteCount > 1 ? 's' : ''}`;
        }
      }
    });

    const selectFormat = (format: string) => {
      selectedFormat.value = format;
    };    const exportItems = async () => {
      if (isExporting.value) return;

      isExporting.value = true;

      // Emit export start event to show progress overlay
      const itemType = getModalTitle.value;
      emit('export-start', {
        format: selectedFormat.value,
        itemCount: props.items.length,
        itemType: itemType
      });

      try {
        if (!selectedFormat.value) {
          throw new Error('No export format selected');
        }

        if (!props.items || props.items.length === 0) {
          throw new Error('No items to export');
        }

        console.log(`Exporting ${props.items.length} items as ${selectedFormat.value}`);
        console.log(`Include subfolders: ${includeSubfolders.value}`);
        console.log(`Include notes: ${includeNotes.value}`);

        // Call the backend API to perform the export
        let result;
        if (props.items.length === 1 && props.items[0].type === 'note') {
          // Single note export
          result = await db.notes.export(props.items[0].id, selectedFormat.value);
        } else {          // Multiple items or folders export
          const options = {
            includeSubfolders: includeSubfolders.value,
            includeNotes: includeNotes.value
          };

          // Create a serializable array of items by extracting only the necessary properties
          const serializableItems = props.items.map(item => ({
            id: item.id,
            type: item.type,
            name: item.name
          }));

          result = await db.notes.exportMultiple(serializableItems, selectedFormat.value, options);
        }
          console.log('Export result:', result);
        emit('export-complete', {
          success: true,
          format: selectedFormat.value,
          // Don't include the items array as it may contain non-serializable data
          message: result
        });
        emit('close');
      } catch (error: any) {
        console.error('Export failed:', error);
        emit('export-complete', {
          success: false,
          error: error.message || 'Unknown error during export'
        });
        emit('close');
      } finally {
        isExporting.value = false;
      }
    };

    return {
      selectedFormat,
      includeSubfolders,
      includeNotes,
      isExporting,
      anyFolders,
      shouldZip,
      getModalTitle,
      getModalSubtitle,
      selectFormat,
      exportItems,
      // Icon imports
      zipIcon
    };
  }
});
</script>

<style scoped>
.export-modal {
  background-color: var(--color-modal-bg);
  border-radius: 12px;
  width: 600px;
  max-width: 95%;
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 30px var(--color-card-shadow);
  animation: slideIn 0.2s ease-out;
  font-family: 'Montserrat', sans-serif;
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.close-icon {
  position: absolute;
  top: 24px;
  right: 24px;
  cursor: pointer;
  z-index: 10;
}

.close-icon img {
  width: 24px;
  height: 24px;
}

.modal-header {
  padding: 40px 40px 20px 40px;
  text-align: left;
}

.modal-title {
  color: var(--color-text-primary);
  font-size: 40px;
  font-weight: 700;
  margin: 0 0 16px 0;
}

.modal-subtitle {
  color: var(--color-text-secondary);
  font-size: 24px;
  font-weight: 400;
  margin: 0;
}

.divider {
  height: 1px;
  background-color: var(--color-border-primary);
  width: 100%;
}

.modal-content {
  padding: 24px 32px;
}

.file-type-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.file-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.file-type-label {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
  font-family: 'Montserrat', sans-serif;
}

.file-type-separator {
  width: 100%;
  height: 1px;
  background-color: var(--color-border-primary);
  margin-bottom: 20px;
}

.zip-status {
  display: flex;
  align-items: center;
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-border-primary);
  border-radius: 6px;
  padding: 10px 16px;
  margin-bottom: 16px;
}

.zip-status-icon {
  width: 18px;
  height: 18px;
  margin-right: 10px;
}

.zip-status-text {
  font-size: 14px;
  color: var(--color-text-secondary);
}

.file-options {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  justify-content: center;
}

.file-option-container {
  flex: 1;
  border: 1px solid var(--color-border-primary);
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
  background-color: var(--color-card-bg);
}

.file-option-container:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 8px var(--color-card-shadow);
  border-color: var(--color-border-hover);
}

.file-option-container.selected {
  border-color: var(--color-primary);
  background-color: var(--color-nav-item-active);
  transform: translateY(-4px);
  box-shadow: 0 4px 8px var(--color-card-hover-shadow);
}

.file-icon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.format-icon-wrapper {
  position: relative;
  display: inline-block;
}

.format-icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.zip-icon {
  position: absolute;
  bottom: -8px;
  right: -8px;
  width: 18px;
  height: 18px;
  background-color: var(--color-modal-bg);
  border-radius: 50%;
  padding: 2px;
  box-shadow: 0 1px 3px var(--color-card-shadow);
}

.export-options {
  margin-top: 20px;
  border-top: 1px solid var(--color-border-primary);
  padding-top: 20px;
}

.export-option {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.export-option input[type="checkbox"] {
  margin-right: 10px;
}

.export-option label {
  color: var(--color-text-primary);
  font-size: 14px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 24px 40px;
  gap: 16px;
}

.btn {
  padding: 12px 24px;
  min-width: 130px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
}

.btn-cancel {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
}

.btn-export {
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
}

.btn-export:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.export-icon {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1); /* Makes the icon white */
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--color-btn-primary-text);
  border-radius: 50%;
  border-top-color: transparent;
  opacity: 0.3;
  animation: spin 1s ease-in-out infinite;
  margin-right: 6px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>
