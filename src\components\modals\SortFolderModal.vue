<template>
  <div class="sort-modal-overlay">
    <div class="sort-modal">
      <div class="close-icon" @click="$emit('close')">
        <img src="/icons/close-icon.svg" alt="Close" />
      </div>
      
      <div class="modal-header">
        <h1 class="modal-title">Sort Contents</h1>
        <p class="modal-subtitle">Sort contents in "{{ folderName }}"</p>
      </div>
      
      <div class="divider"></div>
      
      <div class="modal-content">
        <div class="sort-by-section">
          <div class="section-label">Sort By</div>
          
          <div class="sort-options">
            <div 
              class="sort-option" 
              :class="{ 'selected': sortBy === 'name' }"
              @click="setSortBy('name')"
            >
              <div class="option-icon">
                <img src="/icons/sort-icon.svg" alt="Sort by Name" />
              </div>
              <div class="option-label">Name</div>
            </div>
            
            <div 
              class="sort-option" 
              :class="{ 'selected': sortBy === 'date' }"
              @click="setSortBy('date')"
            >
              <div class="option-icon">
                <img src="/icons/sort-icon.svg" alt="Sort by Date" />
              </div>
              <div class="option-label">Date Modified</div>
            </div>
            
            <div 
              class="sort-option" 
              :class="{ 'selected': sortBy === 'type' }"
              @click="setSortBy('type')"
            >
              <div class="option-icon">
                <img src="/icons/file-type-icon.svg" alt="Sort by Type" />
              </div>
              <div class="option-label">Type</div>
            </div>
            
            <div 
              class="sort-option" 
              :class="{ 'selected': sortBy === 'size' }"
              @click="setSortBy('size')"
            >
              <div class="option-icon">
                <img src="/icons/sort-icon.svg" alt="Sort by Size" />
              </div>
              <div class="option-label">Size</div>
            </div>
          </div>
        </div>
        
        <div class="sort-direction">
          <div class="section-label">Direction</div>
          
          <div class="direction-options">
            <div 
              class="direction-option" 
              :class="{ 'selected': sortDirection === 'ascending' }"
              @click="sortDirection = 'ascending'"
            >
              <img src="/icons/dropdown-arrow-icon.svg" class="up-arrow" alt="Ascending" />
              <span>Ascending (A-Z, Oldest first)</span>
            </div>
            
            <div 
              class="direction-option" 
              :class="{ 'selected': sortDirection === 'descending' }"
              @click="sortDirection = 'descending'"
            >
              <img src="/icons/dropdown-arrow-icon.svg" class="down-arrow" alt="Descending" />
              <span>Descending (Z-A, Newest first)</span>
            </div>
          </div>
        </div>
        
        <div class="group-options">
          <div class="group-option">
            <input type="checkbox" id="foldersFirst" v-model="foldersFirst" />
            <label for="foldersFirst">Show folders before files</label>
          </div>
        </div>
      </div>
      
      <div class="divider"></div>
      
      <div class="modal-footer">
        <button class="btn btn-cancel" @click="$emit('close')">
          Cancel
        </button>
        <button class="btn btn-apply" @click="applySorting">
          <img src="/icons/sort-icon.svg" class="sort-icon" alt="Sort" />
          Apply Sorting
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { useElectronAPI } from '../../useElectronAPI';

export default defineComponent({
  name: 'SortFolderModal',  props: {
    folderId: {
      type: [Number, null],
      default: null
    },
    folderName: {
      type: String,
      default: 'Untitled Folder'
    },
    currentSortBy: {
      type: String,
      default: 'name'
    },
    currentSortDirection: {
      type: String,
      default: 'ascending'
    },
    currentFoldersFirst: {
      type: Boolean,
      default: true
    }
  },
  emits: ['close', 'sort-complete'],  setup(props, { emit }) {
    const db = useElectronAPI();
    const sortBy = ref(props.currentSortBy);
    const sortDirection = ref(props.currentSortDirection);
    const foldersFirst = ref(props.currentFoldersFirst);
    
    const setSortBy = (value: string) => {
      sortBy.value = value;
    };      const applySorting = async () => {
      try {
        console.log(`Sorting folder ${props.folderId === null ? 'Root' : props.folderId}`);
        console.log(`Sort by: ${sortBy.value}`);
        console.log(`Direction: ${sortDirection.value}`);
        console.log(`Folders first: ${foldersFirst.value}`);
        
        // Emit the result immediately - the parent component will handle the actual sorting
        emit('sort-complete', {
          success: true,
          folderId: props.folderId,
          sortBy: sortBy.value,
          sortDirection: sortDirection.value,
          foldersFirst: foldersFirst.value
        });
        
        emit('close');
        
      } catch (error) {
        console.error('Sorting failed:', error);
        emit('sort-complete', { success: false, error });
      }
    };
    
    return {
      sortBy,
      sortDirection,
      foldersFirst,
      setSortBy,
      applySorting
    };
  }
});
</script>

<style scoped>
.sort-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.sort-modal {
  background-color: white;
  border-radius: 12px;
  width: 480px;
  max-width: 95%;
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.close-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6;
  transition: opacity 0.2s;
}

.close-icon:hover {
  opacity: 1;
}

.close-icon img {
  width: 16px;
  height: 16px;
}

.modal-header {
  padding: 24px 32px;
  text-align: left;
}

.modal-title {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin-bottom: 8px;
  font-family: 'Montserrat', sans-serif;
}

.modal-subtitle {
  margin: 0;
  font-size: 14px;
  color: #777;
  font-family: 'Montserrat', sans-serif;
}

.divider {
  height: 1px;
  background-color: #eeeeee;
  width: 100%;
}

.modal-content {
  padding: 24px 32px;
}

.section-label {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  font-family: 'Montserrat', sans-serif;
}

.sort-by-section {
  margin-bottom: 24px;
}

.sort-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.sort-option {
  width: calc(50% - 8px);
  padding: 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.sort-option:hover {
  background-color: #f9f9f9;
  border-color: #ccc;
}

.sort-option.selected {
  background-color: #f5f5f5;
  border-color: #4A4A4A;
}

.option-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.option-icon img {
  width: 20px;
  height: 20px;
  opacity: 0.7;
}

.option-label {
  font-size: 14px;
  color: #333;
  font-family: 'Montserrat', sans-serif;
}

.sort-direction {
  margin-bottom: 24px;
}

.direction-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.direction-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.direction-option:hover {
  background-color: #f9f9f9;
  border-color: #ccc;
}

.direction-option.selected {
  background-color: #f5f5f5;
  border-color: #4A4A4A;
}

.up-arrow {
  width: 16px;
  height: 16px;
  transform: rotate(180deg);
  opacity: 0.7;
}

.down-arrow {
  width: 16px;
  height: 16px;
  opacity: 0.7;
}

.direction-option span {
  font-size: 14px;
  color: #333;
  font-family: 'Montserrat', sans-serif;
}

.group-options {
  margin-top: 24px;
}

.group-option {
  display: flex;
  align-items: center;
  gap: 12px;
}

.group-option input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #4A4A4A;
}

.group-option label {
  font-size: 14px;
  color: #333;
  font-family: 'Montserrat', sans-serif;
}

.modal-footer {
  padding: 16px 32px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  font-family: 'Montserrat', sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-cancel {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  color: #555;
}

.btn-cancel:hover {
  background-color: #e5e5e5;
}

.btn-apply {
  background-color: #4A4A4A;
  border: none;
  color: white;
}

.btn-apply:hover {
  background-color: #333333;
}

.sort-icon {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1);
}
</style>
