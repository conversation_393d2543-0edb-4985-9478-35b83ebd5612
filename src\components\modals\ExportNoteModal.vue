<template>
  <div class="export-modal">
    <div class="close-icon" @click="$emit('close')">
      <img :src="closeIcon" alt="Close" />
    </div>

    <div class="modal-header">
      <h1 class="modal-title">Export Note</h1>
      <p class="modal-subtitle">Export "{{ noteTitle }}" to a file format</p>
    </div>

    <div class="divider"></div>

    <div class="modal-content">
      <div class="file-type-header">
        <img :src="fileIcon" class="file-icon" alt="File" />
        <span class="file-type-label">File type</span>
      </div>

      <div class="file-type-separator"></div>

      <div class="file-options">
        <div
          class="file-option-container"
          :class="{ 'selected': selectedFormat === 'pdf' }"
          @click="selectFormat('pdf')"
        >
          <div class="file-icon-container">
            <img :src="pdfIcon" alt="PDF" class="format-icon" />
          </div>
          <div class="format-label">PDF</div>
        </div>

        <div
          class="file-option-container"
          :class="{ 'selected': selectedFormat === 'md' }"
          @click="selectFormat('md')"
        >
          <div class="file-icon-container">
            <img :src="mdIcon" alt="MD" class="format-icon" />
          </div>
          <div class="format-label">Markdown</div>
        </div>

        <div
          class="file-option-container"
          :class="{ 'selected': selectedFormat === 'noti' }"
          @click="selectFormat('noti')"
        >
          <div class="file-icon-container">
            <img :src="notiIcon" alt="NOTI" class="format-icon" />
          </div>
          <div class="format-label">Noti</div>
        </div>
      </div>
    </div>

    <div class="divider"></div>

    <div class="modal-footer">
      <button class="btn btn-cancel" @click="$emit('close')">
        Cancel
      </button>
      <button class="btn btn-export" @click="exportNote" :disabled="!selectedFormat || isExporting">
        <span v-if="isExporting" class="loading-spinner"></span>
        <img v-if="!isExporting" :src="exportIcon" class="export-icon" alt="Export" />
        {{ isExporting ? 'Preparing export...' : 'Export' }}
      </button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { useElectronAPI } from '../../useElectronAPI';

// Import icons as modules for proper path resolution in production
import closeIcon from '/icons/close-icon.svg'
import fileIcon from '/icons/file-icon.svg'
import pdfIcon from '/icons/pdf-icon.svg'
import mdIcon from '/icons/md-icon.svg'
import notiIcon from '/icons/noti-icon.svg'
import exportIcon from '/icons/export-icon.svg'

export default defineComponent({
  name: 'ExportNoteModal',
  props: {
    noteId: {
      type: Number,
      required: true
    },
    noteTitle: {
      type: String,
      default: 'Untitled Note'
    }
  },
  emits: ['close', 'export-start', 'export-complete'],
  setup(props, { emit }) {
    const db = useElectronAPI();
    const selectedFormat = ref<string>('');
    const isExporting = ref<boolean>(false);

    const selectFormat = (format: string) => {
      selectedFormat.value = format;
    };

    const exportNote = async () => {
      if (!selectedFormat.value || isExporting.value) return;

      isExporting.value = true;

      // Emit export start event to show progress overlay
      emit('export-start', {
        format: selectedFormat.value,
        itemCount: 1,
        itemType: 'Note'
      });

      try {
        console.log(`Exporting note ${props.noteId} as ${selectedFormat.value}`);

        // Call the backend API to perform the export
        const result = await db.notes.export(props.noteId, selectedFormat.value);

        console.log('Export result:', result);

        emit('export-complete', {
          success: true,
          format: selectedFormat.value,
          noteId: props.noteId,
          message: result
        });
        emit('close');

      } catch (error: any) {
        console.error('Export failed:', error);
        emit('export-complete', {
          success: false,
          error: error.message || 'Unknown error during export'
        });
        emit('close');
      } finally {
        isExporting.value = false;
      }
    };

    return {
      selectedFormat,
      isExporting,
      selectFormat,
      exportNote,
      // Icon imports
      closeIcon,
      fileIcon,
      pdfIcon,
      mdIcon,
      notiIcon,
      exportIcon
    }
  }
})
</script>

<style scoped>
.export-modal {
  max-width: 700px;
  width: 90%;
  background-color: var(--color-modal-bg);
  border-radius: 16px;
  box-shadow: 0px 4px 30px var(--color-card-shadow);
  position: relative;
  overflow: hidden;
  font-family: 'Montserrat', sans-serif;
  z-index: 10000;
}

.close-icon {
  position: absolute;
  top: 24px;
  right: 24px;
  cursor: pointer;
  z-index: 10;
}

.close-icon img {
  width: 24px;
  height: 24px;
}

.modal-header {
  padding: 40px 40px 20px 40px;
  text-align: left;
}

.modal-title {
  color: var(--color-text-primary);
  font-size: 40px;
  font-weight: 700;
  margin: 0 0 16px 0;
}

.modal-subtitle {
  color: var(--color-text-secondary);
  font-size: 24px;
  font-weight: 400;
  margin: 0;
}

.divider {
  height: 1px;
  background-color: var(--color-border-primary);
  width: 100%;
}

.modal-content {
  padding: 40px;
  text-align: left;
}

.file-type-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--color-text-secondary);
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.file-icon {
  width: 24px;
  height: 24px;
}

.file-type-separator {
  height: 1px;
  background-color: var(--color-border-secondary);
  width: 100%;
  margin-bottom: 24px;
}

.file-options {
  display: flex;
  justify-content: center;
  gap: 40px;
  padding: 24px 0;
}

.file-option-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 20px;
  border-radius: 12px;
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.file-option-container:hover {
  transform: translateY(-8px);
  background-color: var(--color-nav-item-hover);
}

.file-option-container.selected {
  transform: translateY(-8px);
  background-color: var(--color-nav-item-active);
  box-shadow: 0 4px 8px var(--color-card-shadow);
}

.file-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.format-icon {
  width: 64px;
  height: 76px;
  object-fit: contain;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 24px 40px;
  gap: 16px;
}

.btn {
  padding: 12px 24px;
  min-width: 130px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
}

.btn-cancel {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
}

.btn-export {
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
}

.btn-export:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.export-icon {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1); /* This makes the icon white */
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--color-btn-primary-text);
  border-radius: 50%;
  border-top-color: transparent;
  opacity: 0.3;
  animation: spin 1s ease-in-out infinite;
  margin-right: 6px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>